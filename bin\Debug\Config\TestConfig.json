{"Project": "AppSe5xCheckInfo", "Version": "2.0.0.0", "UpdateTime": "2025-07-28 16:00:00", "Data": [{"Name": "产品通信", "CmdStr": "AT+PWD=6789", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "Password OK", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "产品通信", "IsEnabled": true, "RegexPattern": "Password OK", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "Password OK", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取设备SN", "CmdStr": "AT+NEWMARK", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "new mark", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取设备SN", "IsEnabled": true, "RegexPattern": "new mark.*", "CutTextType": 2, "CutTextFlag1": ":", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 3, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "获取PCBA检测数据", "CmdStr": "", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "获取PCBA数据结果", "WaitTime": 250, "WaitCount": 4, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "获取PCBA检测数据", "IsEnabled": true, "RegexPattern": "获取PCBA数据结果成功", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "获取PCBA数据结果成功", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "读取设备信息", "CmdStr": "AT+INFO", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "Bike Type flag is invalied", "WaitTime": 250, "WaitCount": 8, "Retry": 8, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取设备LockID", "IsEnabled": true, "RegexPattern": "LockID.*", "CutTextType": 2, "CutTextFlag1": "=", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 3, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "读取设备IMEI", "IsEnabled": true, "RegexPattern": "HardwareID.*", "CutTextType": 2, "CutTextFlag1": "=", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 3, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "读取设备IMSI", "IsEnabled": true, "RegexPattern": "DeviceID.*", "CutTextType": 2, "CutTextFlag1": "=", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 3, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "校验IMEI", "CmdStr": "", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "校验IMEI数据明细", "WaitTime": 250, "WaitCount": 1, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "校验IMEI", "IsEnabled": true, "RegexPattern": "原始IMEI.*", "CutTextType": 2, "CutTextFlag1": ":", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "设备IMEI", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "校验IMSI", "CmdStr": "", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "校验IMSI数据明细", "WaitTime": 200, "WaitCount": 1, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "校验IMSI", "IsEnabled": true, "RegexPattern": "原始IMSI.*", "CutTextType": 2, "CutTextFlag1": ":", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "设备IMSI", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "校验LockID", "CmdStr": "", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "扫描到的LockID", "WaitTime": 1000, "WaitCount": 30, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "校验LockID", "IsEnabled": true, "RegexPattern": "扫描到的LockID.*", "CutTextType": 2, "CutTextFlag1": ":", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "设备LockID", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "MES过站", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "MES过站", "IsEnabled": true, "RegexPattern": "^[A-Za-z]+", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "True", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 10.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}]}