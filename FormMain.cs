﻿// ====================================================================
// AppSe5xCheckInfo测试系统 - 主窗体类
// 功能：自动化测试系统
// 作者：博实结
// 版本：2.0.0.0
// 创建时间：2025年
// ====================================================================

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using AiHelper;
using AppSe5xCheckInfo.Config;
using AppSe5xCheckInfo.Models;
using BSJ.AI.MES.DynamicInvocation;
using BSJ.AI.MES.DynamicInvocation.Model;
using BsjHelperV2.Enum;
using BsjHelperV2.Helper;
using BsjHelperV2.Models;
using BsjHelperV2.UI;
using Newtonsoft.Json;
using RJCP.IO.Ports;
using Sunny.UI;
using Parity = RJCP.IO.Ports.Parity; // 串口校验位
using StopBits = RJCP.IO.Ports.StopBits; // 串口停止位
using SerialDataReceivedEventArgs = RJCP.IO.Ports.SerialDataReceivedEventArgs; // 串口数据接收事件参数
using SerialErrorReceivedEventArgs = RJCP.IO.Ports.SerialErrorReceivedEventArgs; // 串口错误事件参数
using Timer = System.Windows.Forms.Timer; // Windows窗体定时器

namespace AppSe5xCheckInfo
{
    /// <summary>
    /// AppSe5xCheckInfo测试系统主窗体类
    /// </summary>
    public partial class FormMain : UIForm
    {
        #region 全局变量属性

        /// <summary>
        /// 应用程序名称和版本信息
        /// 显示在窗体标题栏
        /// </summary>
        private string AppNameVersion { get; set; } = $"博实结-AppSe5xCheckInfo测试 - V:{Application.ProductVersion}";

        /// <summary>
        /// 线程安全的日志队列
        /// 用于在多线程环境下安全地处理日志消息
        /// </summary>
        private readonly ConcurrentQueue<CustomLog> _logQueue = new ConcurrentQueue<CustomLog>();

        /// <summary>
        /// 日志处理定时器
        /// 定期从日志队列中取出消息并显示到界面
        /// </summary>
        private Timer _logTimer;

        /// <summary>
        /// 取消令牌源
        /// 用于控制测试任务的取消操作
        /// </summary>
        private CancellationTokenSource Cts { get; set; }

        /// <summary>
        /// 测试配置对象
        /// 包含所有测试项的配置信息，从JSON文件加载
        /// </summary>
        private TestConfig TestConfigBsj { get; set; }

        /// <summary>
        /// 显示用的测试项列表
        /// 用于在界面表格中显示测试项信息
        /// </summary>
        private List<ShowTestItem> ShowTestItemList { get; set; } = new List<ShowTestItem>();

        /// <summary>
        /// 当前测试的设备对象
        /// 包含设备SN、测试结果等信息
        /// </summary>
        private Se5XDeviceCkInfo TestDeviceCkInfo { get; set; }

        private Se5XDevice Se5XDevicePcba { get; set; }

        /// <summary>
        /// 测试项耗时字典
        /// 记录每个测试项的执行时间，用于性能分析
        /// </summary>
        private Dictionary<string, long> TestItemTimeDic { get; set; } = new Dictionary<string, long>();

        /// <summary>
        /// 设备通信串口对象
        /// 用于与被测设备进行通信，发送测试指令和接收测试数据
        /// </summary>
        private SerialPortStream SerialPortHelperDevice { get; set; }

        /// <summary>
        /// 扫码枪串口对象
        /// 用于接收条码扫描器扫描的SN码，触发测试流程
        /// </summary>
        private SerialPortStream SerialPortHelperScan { get; set; }

        /// <summary>
        /// 串口数据临时存储缓冲区
        /// 用于累积接收到的串口数据，直到收到完整的响应
        /// </summary>
        private StringBuilder SbDevice { get; set; } = new StringBuilder();

        /// <summary>
        /// 测试总耗时（秒）
        /// 记录整个测试流程的总时间
        /// </summary>
        private int TestTime { get; set; }

        /// <summary>
        /// MES系统连接状态
        /// true：已连接MES系统，false：离线模式
        /// </summary>
        private bool IsMesConnect { get; set; }

        /// <summary>
        /// 数据库连接状态
        /// true：数据库连接正常，false：数据库连接失败
        /// </summary>
        private bool IsDbConnect { get; set; }

        /// <summary>
        /// 重载Windows消息处理方法
        /// 主要用于监听USB设备插拔事件，自动刷新串口列表
        /// 当有USB设备插入或拔出时，会自动更新可用的串口列表
        /// </summary>
        /// <param name="m">Windows消息对象</param>
        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m); // 调用父类方法，确保其他Windows消息正常处理

            switch (m.Msg)
            {
                case 0x219: // WM_DEVICECHANGE - 设备改变事件
                    switch ((int)m.WParam)
                    {
                        case 0x8000: // DBT_DEVICEARRIVAL - 检测到新设备插入
                            UpdateSerialPort(); // 刷新串口列表
                            break;

                        case 0x8004: // DBT_DEVICEREMOVECOMPLETE - 有设备被移除
                            UpdateSerialPort(); // 刷新串口列表
                            break;
                    }

                    break;
            }
        }

        #endregion 全局变量属性

        #region 构造函数

        /// <summary>
        /// 主窗体构造函数
        /// 初始化界面组件和基础配置
        /// </summary>
        public FormMain()
        {
            // 初始化界面组件（由设计器生成）
            InitializeComponent();

            // 启用窗体拖拽缩放功能
            ShowDragStretch = true;

            // 加载应用程序配置
            Setting.Current.Load();

            // 初始化日志处理定时器
            InitializeLogTimer();
        }

        #endregion 构造函数

        #region UI控件事件

        /// <summary>
        /// 主窗体加载事件处理方法
        /// 在窗体首次显示时执行初始化操作
        /// </summary>
        /// <param name="sender">事件发送者（主窗体）</param>
        /// <param name="e">事件参数</param>
        private void FormMain_Load(object sender, EventArgs e)
        {
            // 设置窗体标题为应用程序名称和版本
            Text = AppNameVersion;

            // 停止时间显示定时器（初始状态）
            timer_ShowTime.Stop();

            // 加载测试配置文件
            LoadTestConfig();

            // 从配置文件中恢复MES账号和站位信息
            uiTextBox_MesAccount.Text = Setting.Current.UserName;
            uiTextBox_MesStationName.Text = Setting.Current.StationName;

            // 初始化数据库连接
            DbInit();

            // 初始化MES系统连接
            MesInit();

            // 更新界面内容
            UpdateSerialPort(); // 刷新串口列表
            ShowTestItemsToListView(); // 显示测试项到右侧列表
            ShowTestItemToEditDgv(); // 显示测试项到编辑表格
        }

        /// <summary>
        /// 从JSON文件加载测试项配置
        /// 配置文件包含所有测试项的参数、指令、判断条件等
        /// </summary>
        private void LoadTestConfig()
        {
            try
            {
                // 读取测试配置文件并反序列化为对象
                string configPath = Application.StartupPath + "/config/TestConfig.json";
                string json = File.ReadAllText(configPath);
                TestConfigBsj = JsonConvert.DeserializeObject<TestConfig>(json);

                ShowLog($"测试配置加载成功，项目：{TestConfigBsj.Project}，版本：{TestConfigBsj.Version}", LogType.Info);
            }
            catch (Exception ex)
            {
                ShowLog($"测试配置加载失败：{ex.Message}", LogType.Error);
                // 如果配置文件加载失败，创建默认配置
                TestConfigBsj = new TestConfig();
            }
        }

        /// <summary>
        /// 将启用的测试项显示到右侧ListView控件中
        /// 只显示IsEnabled=true的测试项，并按顺序编号
        /// 每个测试项显示为一行，包含序号和名称
        /// </summary>
        private void ShowTestItemsToListView()
        {
            // 清空现有的列表项
            listView_TestItems.Items.Clear();
            int step = 0; // 测试项序号计数器

            // 遍历所有测试项组
            foreach (TestItem testItem in TestConfigBsj.Data)
            {
                // 遍历每个测试项组中的参数
                foreach (TestItemParameter param in testItem.Params)
                {
                    // 跳过禁用的测试参数
                    if (!param.IsEnabled)
                    {
                        continue;
                    }

                    // 序号递增
                    step++;

                    // 添加到ListView，格式：序号.测试项名称
                    // 参数说明：Key=参数名称, Text=显示文本, ImageIndex=图标索引(0=等待状态)
                    listView_TestItems.Items.Add($"{param.Name}", $"{step}.{param.Name}", 0);

                    // 调试输出：显示错误码和对应的测试项名称
                    Debug.WriteLine($"ErrorCode: {param.ErrorCode}, 对应测试项: {param.Name}");
                }
            }

            ShowLog($"测试项列表已更新，共{step}个启用的测试项", LogType.Info);
        }

        /// <summary>
        /// 将测试项配置显示到编辑表格中
        /// 用于在设置页面中查看和编辑测试项参数
        /// 包含所有测试项的详细配置信息（启用和禁用的都显示）
        /// </summary>
        private void ShowTestItemToEditDgv()
        {
            // 清空现有的显示列表
            ShowTestItemList.Clear();
            int paramIndex = 0; // 参数索引计数器

            // 遍历所有测试项组
            foreach (TestItem testItem in TestConfigBsj.Data)
            {
                // 遍历每个测试项组中的参数
                foreach (TestItemParameter param in testItem.Params)
                {
                    paramIndex++; // 索引递增

                    // 创建显示用的测试项对象，包含完整的配置信息
                    ShowTestItem tmpData = new ShowTestItem
                    {
                        Index = paramIndex, // 序号
                        IsEnabled = param.IsEnabled, // 是否启用
                        SleepTime = testItem.SleepTime, // 执行前等待时间
                        Name = param.Name, // 测试项名称
                        CmdStr = testItem.CmdStr, // 测试指令字符串
                        AsciiSend = testItem.AsciiSend, // 是否以ASCII格式发送
                        EnterLine = testItem.EnterLine, // 是否添加回车换行
                        ClearData = testItem.ClearData, // 是否清空接收缓冲区
                        EndWithStr = testItem.EndWithStr, // 结束标志字符串
                        WaitTime = testItem.WaitTime, // 单次等待时间(ms)
                        WaitCount = testItem.WaitCount, // 最大等待次数
                        Retry = testItem.Retry, // 重试次数
                        Remark = param.Remark // 备注信息
                    };
                    ShowTestItemList.Add(tmpData);
                }
            }

            // 在UI线程中更新表格显示
            Invoke(new Action(() =>
            {
                UIDataGridView dgv = uiDataGridView_TestItem;

                // 暂时隐藏表格以提高性能
                dgv.Visible = false;

                // 清空并重新绑定数据源
                dgv.DataSource = null;
                dgv.DataSource = ShowTestItemList;

                // 设置列标题为中文显示
                if (dgv.Columns["Index"] != null) dgv.Columns["Index"].HeaderText = @"序号";
                if (dgv.Columns["IsEnabled"] != null) dgv.Columns["IsEnabled"].HeaderText = @"启用";
                if (dgv.Columns["Name"] != null) dgv.Columns["Name"].HeaderText = @"名称";
                if (dgv.Columns["CmdStr"] != null) dgv.Columns["CmdStr"].HeaderText = @"指令";
                if (dgv.Columns["AsciiSend"] != null) dgv.Columns["AsciiSend"].HeaderText = @"字符";
                if (dgv.Columns["EnterLine"] != null) dgv.Columns["EnterLine"].HeaderText = @"换行";
                if (dgv.Columns["ClearData"] != null) dgv.Columns["ClearData"].HeaderText = @"清除";
                if (dgv.Columns["EndWithStr"] != null) dgv.Columns["EndWithStr"].HeaderText = @"结束字符串";
                if (dgv.Columns["WaitTime"] != null) dgv.Columns["WaitTime"].HeaderText = @"等待时间";
                if (dgv.Columns["WaitCount"] != null) dgv.Columns["WaitCount"].HeaderText = @"等待次数";
                if (dgv.Columns["Retry"] != null) dgv.Columns["Retry"].HeaderText = @"重试次数";

                // 自动调整列宽以适应内容
                dgv.AutoResizeColumns();

                // 重新显示表格
                dgv.Visible = true;
            }));

            ShowLog($"测试项编辑表格已更新，共{paramIndex}个测试项", LogType.Info);
        }

        /// <summary>
        /// 更新串口列表
        /// 扫描系统中所有可用的串口，并更新到下拉框中
        /// 同时恢复之前保存的串口配置
        /// </summary>
        private void UpdateSerialPort()
        {
            // 清空现有的串口列表
            uiComboBox_SerialPortDevice.Items.Clear();
            uiComboBox_SerialPortScan.Items.Clear();

            // 获取系统中所有可用的串口，并按名称排序
            string[] ports = SerialPort.GetPortNames().OrderBy(x => x).ToArray();

            // 将串口添加到两个下拉框中
            foreach (string port in ports)
            {
                uiComboBox_SerialPortDevice.Items.Add(port); // 设备通信串口
                uiComboBox_SerialPortScan.Items.Add(port); // 扫码枪串口
            }

            // 恢复之前保存的串口配置，如果配置的串口不存在则使用第一个可用串口
            uiComboBox_SerialPortDevice.Text = uiComboBox_SerialPortDevice.Items.Contains(Setting.Current.SerialPortDevice)
                ? Setting.Current.SerialPortDevice
                : ports.FirstOrDefault();

            uiComboBox_SerialPortScan.Text = uiComboBox_SerialPortScan.Items.Contains(Setting.Current.SerialPortScan)
                ? Setting.Current.SerialPortScan
                : ports.FirstOrDefault();

            ShowLog($"串口列表已更新，发现{ports.Length}个可用串口", LogType.Info);
        }

        /// <summary>
        /// 初始化数据库连接
        /// 测试数据库连接是否正常，并设置连接状态标志
        /// 数据库用于存储测试结果和设备信息
        /// </summary>
        private void DbInit()
        {
            try
            {
                // 尝试查询数据库中的第一条测试详情记录来验证连接
                TestDetail detail = DbHelper.FSql.Select<TestDetail>().First();

                // 设置数据库连接状态（即使查询结果为null也说明连接正常）
                IsDbConnect = detail != null;

                // 更新界面上的数据库状态开关
                uiSwitch_DBS.Active = IsDbConnect;

                if (IsDbConnect)
                {
                    ShowLog(@"数据库连接已打开.", LogType.Info);
                }
                else
                {
                    string tips = "数据库连接失败.若继续测试，将不记录测试结果到数据库.";
                    ShowLog(tips, LogType.Error);
                    // 异步显示错误对话框，避免阻塞UI线程
                    Task.Run(() => this.ShowErrorDialog("错误提醒", tips));
                }
            }
            catch (Exception ex)
            {
                // 数据库连接异常处理
                IsDbConnect = false;
                uiSwitch_DBS.Active = false;
                string errorMsg = $"数据库初始化异常：{ex.Message}";
                ShowLog(errorMsg, LogType.Error);
                Task.Run(() => this.ShowErrorDialog("数据库错误", errorMsg));
            }
        }

        /// <summary>
        /// 初始化MES（制造执行系统）连接
        /// 验证MES账号有效性，并根据连接状态决定是否启用MES过站功能
        /// MES用于生产过程管理和质量追溯
        /// </summary>
        private void MesInit()
        {
            try
            {
                // 设置MES服务的用户名
                MESService.UserName = Setting.Current.UserName;

                // 验证MES账号有效性
                CallBackResultModel<ReturnDataModel> result = MESService.ValidateAccount();

                // 设置MES连接状态
                IsMesConnect = result.Success;

                // 更新界面上的MES状态开关
                uiSwitch_MES.Active = IsMesConnect;

                // 根据MES连接状态决定是否启用MES过站测试项
                var mesTestItem = TestConfigBsj.Data.Find(x => x.Name == "MES过站");
                if (mesTestItem != null)
                {
                    var mesParam = mesTestItem.Params.Find(x => x.Name == "MES过站");
                    if (mesParam != null)
                    {
                        mesParam.IsEnabled = IsMesConnect;
                    }
                }

                // 显示MES连接状态日志
                if (IsMesConnect)
                {
                    ShowLog($@"MES账号验证: {MESService.UserName}, {result.Msg}", LogType.Info);
                    ShowLog(@"MES连接已打开.", LogType.Info);
                }
                else
                {
                    ShowLog($@"MES账号验证: {MESService.UserName}, {result.Msg}", LogType.Error);
                    ShowLog("MES连接失败，将切换为离线测试模式", LogType.Warning);
                    this.ShowErrorDialog("MES账号验证失败", "请联系管理员，即将切换为离线测试模式");
                }
            }
            catch (Exception ex)
            {
                // MES初始化异常处理
                IsMesConnect = false;
                uiSwitch_MES.Active = false;
                ShowLog($"MES初始化异常：{ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// 串口开关按钮点击事件处理方法
        /// 根据当前串口状态执行打开或关闭操作
        /// 同时更新按钮文本和样式以反映当前状态
        /// </summary>
        /// <param name="sender">事件发送者（串口开关按钮）</param>
        /// <param name="e">事件参数</param>
        private void uiButton_SerialPortOpenClose_Click(object sender, EventArgs e)
        {
            // 根据按钮文本判断当前状态并执行相应操作
            if (uiButton_SerialPortOpenClose.Text == @"打开串口")
            {
                // 尝试打开串口
                if (!SerialPortOpen())
                {
                    // 如果打开失败，确保串口完全关闭
                    SerialPortClose();
                    return;
                }

                // 打开成功，更新按钮状态
                uiButton_SerialPortOpenClose.Text = @"关闭串口";
                uiButton_SerialPortOpenClose.Style = UIStyle.Green; // 绿色表示已连接
            }
            else
            {
                // 尝试关闭串口
                if (SerialPortClose())
                {
                    // 关闭成功，更新按钮状态
                    uiButton_SerialPortOpenClose.Text = @"打开串口";
                    uiButton_SerialPortOpenClose.Style = UIStyle.Blue; // 蓝色表示未连接
                }
            }
        }

        /// <summary>
        /// 打开串口连接
        /// 同时打开设备通信串口和扫码枪串口
        /// 配置串口参数并绑定事件处理方法
        /// </summary>
        /// <returns>true：串口打开成功，false：串口打开失败</returns>
        private bool SerialPortOpen()
        {
            // 保存当前选择的串口配置到设置文件
            Setting.Current.SerialPortDevice = uiComboBox_SerialPortDevice.Text;
            Setting.Current.SerialPortScan = uiComboBox_SerialPortScan.Text;
            Setting.Current.Save();

            // 创建并配置设备通信串口
            try
            {
                // 创建设备串口对象
                SerialPortHelperDevice = new SerialPortStream(Setting.Current.SerialPortDevice);

                // 配置串口参数
                SerialPortHelperDevice.BaudRate = Setting.Current.SerialPortDeviceBaudRate; // 波特率
                SerialPortHelperDevice.DataBits = 8; // 数据位
                SerialPortHelperDevice.StopBits = StopBits.One; // 停止位
                SerialPortHelperDevice.Parity = Parity.None; // 校验位

                // 绑定事件处理方法
                SerialPortHelperDevice.DataReceived += SerialPortDataReceivedDevice; // 数据接收事件
                SerialPortHelperDevice.ErrorReceived += SerialPortErrorDevice; // 错误事件

                // 配置控制信号
                SerialPortHelperDevice.DtrEnable = true; // 数据终端就绪信号
                SerialPortHelperDevice.RtsEnable = true; // 请求发送信号

                // 配置超时时间
                SerialPortHelperDevice.ReadTimeout = 1000; // 读取超时1秒
                SerialPortHelperDevice.WriteTimeout = 1000; // 写入超时1秒

                // 打开串口
                SerialPortHelperDevice.Open();
                ShowLog($"设备串口[{SerialPortHelperDevice.PortName}]打开成功", LogType.Info);
            }
            catch (Exception ex)
            {
                ShowLog($"设备串口[{Setting.Current.SerialPortDevice}]打开失败：{ex.Message}", LogType.Error);
                return false;
            }

            // 创建并配置扫码枪串口
            try
            {
                // 创建扫码串口对象
                SerialPortHelperScan = new SerialPortStream(Setting.Current.SerialPortScan);

                // 配置串口参数
                SerialPortHelperScan.BaudRate = Setting.Current.SerialPortScanBaudRate; // 波特率
                SerialPortHelperScan.DataBits = 8; // 数据位
                SerialPortHelperScan.StopBits = StopBits.One; // 停止位
                SerialPortHelperScan.Parity = Parity.None; // 校验位

                // 绑定事件处理方法
                SerialPortHelperScan.DataReceived += SerialPortDataReceivedScan; // 数据接收事件
                SerialPortHelperScan.ErrorReceived += SerialPortErrorScan; // 错误事件

                // 配置控制信号
                SerialPortHelperScan.DtrEnable = true; // 数据终端就绪信号
                SerialPortHelperScan.RtsEnable = true; // 请求发送信号

                // 配置超时时间
                SerialPortHelperScan.ReadTimeout = 1000; // 读取超时1秒
                SerialPortHelperScan.WriteTimeout = 1000; // 写入超时1秒

                // 打开串口
                SerialPortHelperScan.Open();
                ShowLog($"扫码串口[{SerialPortHelperScan.PortName}]打开成功", LogType.Info);
            }
            catch (Exception ex)
            {
                ShowLog($"扫码串口[{Setting.Current.SerialPortScan}]打开失败：{ex.Message}", LogType.Error);
                return false;
            }

            // 两个串口都打开成功
            ShowLog("所有串口打开成功，系统准备就绪", LogType.Info);
            return true;
        }

        /// <summary>
        /// 关闭串口连接
        /// 安全地关闭设备通信串口和扫码枪串口
        /// </summary>
        /// <returns>true：串口关闭成功，false：串口关闭失败</returns>
        private bool SerialPortClose()
        {
            bool success = true;

            // 关闭设备通信串口
            if (SerialPortHelperDevice != null && SerialPortHelperDevice.IsOpen)
            {
                try
                {
                    SerialPortHelperDevice.Close();
                    ShowLog($"设备串口[{SerialPortHelperDevice.PortName}]关闭成功", LogType.Info);
                }
                catch (Exception ex)
                {
                    ShowLog($"设备串口[{SerialPortHelperDevice.PortName}]关闭失败：{ex.Message}", LogType.Error);
                    success = false;
                }
            }

            // 关闭扫码枪串口
            if (SerialPortHelperScan != null && SerialPortHelperScan.IsOpen)
            {
                try
                {
                    SerialPortHelperScan.Close();
                    ShowLog($"扫码串口[{SerialPortHelperScan.PortName}]关闭成功", LogType.Info);
                }
                catch (Exception ex)
                {
                    ShowLog($"扫码串口[{SerialPortHelperScan.PortName}]关闭失败：{ex.Message}", LogType.Error);
                    success = false;
                }
            }

            if (success)
            {
                ShowLog("所有串口已关闭", LogType.Info);
            }

            return success;
        }

        /// <summary>
        /// MES连接开关点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSwitch_MES_Click(object sender, EventArgs e)
        {
            if (IsMesConnect)
            {
                IsMesConnect = false;
                ShowLog("MES连接已关闭.", LogType.Error);
                uiSwitch_MES.Active = IsMesConnect;
                TestConfigBsj.Data.Find(x => x.Name == "MES过站").Params.Find(x => x.Name == "MES过站").IsEnabled = IsMesConnect;
            }
            else
            {
                Setting.Current.UserName = uiTextBox_MesAccount.Text;
                Setting.Current.StationName = uiTextBox_MesStationName.Text;
                Setting.Current.Save();
                MesInit();
            }

            // 不管打开还是关闭，都需要重新显示测试项列表和编辑表格
            ShowTestItemsToListView();
            ShowTestItemToEditDgv();
        }

        /// <summary>
        /// 数据库连接开关点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSwitch_DBS_Click(object sender, EventArgs e)
        {
            if (IsDbConnect)
            {
                IsDbConnect = false;
                ShowLog("数据库连接已关闭.", LogType.Error);
                uiSwitch_DBS.Active = IsDbConnect;
            }
            else
            {
                DbInit();
            }
        }

        /// <summary>
        /// 显示测试时间
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void timer_ShowTime_Tick(object sender, EventArgs e)
        {
            try
            {
                TestTime++;
                uiLabel_Status.Text = $@"{TestTime}";
            }
            catch (Exception ex)
            {
                ShowLog(ex.ToString(), LogType.Error);
            }
        }

        /// <summary>
        /// 编辑测试项
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiDataGridView_TestItem_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0)
            {
                return;
            }

            // 使用Linq语法获取当前行的数据
            var foundItem = TestConfigBsj.Data
                .SelectMany((testItem, tmpTestItemIndex) =>
                    testItem.Params.Select((param, tmpParamIndex) =>
                        new { testItem, tmpTestItemIndex, param, tmpParamIndex }))
                .Where((item, totalIndex) => totalIndex == e.RowIndex)
                .FirstOrDefault();

            Debug.Assert(foundItem != null, nameof(foundItem) + " != null");
            int testItemIndex = foundItem.tmpTestItemIndex;
            int paramIndex = foundItem.tmpParamIndex;

            // 赋值测试项数据
            BsjHelperV2.UI.UIEditOption option = new BsjHelperV2.UI.UIEditOption { AutoLabelWidth = true, Text = "编辑测试项" };
            option.AddText("TestItemName", "一级名称", TestConfigBsj.Data[testItemIndex].Name, false);
            option.AddText("TestItemCmdStr", "指令内容", TestConfigBsj.Data[testItemIndex].CmdStr, false);
            option.AddSwitch("TestItemSwitchAsciiSend", "ASCII发送", TestConfigBsj.Data[testItemIndex].AsciiSend, "打开", "关闭");
            option.AddSwitch("TestItemSwitchEnterLine", "加回车换行", TestConfigBsj.Data[testItemIndex].EnterLine, "打开", "关闭");
            option.AddSwitch("TestItemSwitchClearData", "先清除旧数据", TestConfigBsj.Data[testItemIndex].ClearData, "打开", "关闭");
            option.AddText("TestItemEndWithStr", "结束字符串", TestConfigBsj.Data[testItemIndex].EndWithStr, false);
            option.AddInteger("TestItemWaitTime", "等待时间", TestConfigBsj.Data[testItemIndex].WaitTime);
            option.AddInteger("TestItemWaitCount", "等待次数", TestConfigBsj.Data[testItemIndex].WaitCount);
            option.AddInteger("TestItemRetry", "失败重试次数", TestConfigBsj.Data[testItemIndex].Retry);
            option.AddInteger("SleepTime", "延时执行(毫秒)", TestConfigBsj.Data[testItemIndex].SleepTime);

            // 编辑参数数据
            option.AddText("ParamName", "二级名称", TestConfigBsj.Data[testItemIndex].Params[paramIndex].Name, false);
            option.AddSwitch("ParamSwitch", "检测状态", TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsEnabled, "打开", "关闭");
            option.AddText("ParamRegexPattern", "正则表达式", TestConfigBsj.Data[testItemIndex].Params[paramIndex].RegexPattern, false);
            string[] dataSourceEnumCutTextType = Enum.GetValues(typeof(EnumCutTextType)).Cast<EnumCutTextType>().ToList().Select(x => x.ToString()).ToArray();
            option.AddCombobox("ParamCutTextType", "数据截取", dataSourceEnumCutTextType, (int)TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextType);
            option.AddText("ParamCutTextFlag1", "截取标识1", TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag1, false);
            option.AddText("ParamCutTextFlag2", "截取标识2", TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag2, false);
            option.AddSwitch("ParamSwitchIsCompareText", "比较文本", TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsCompareText, "打开", "关闭");
            option.AddText("ParamTargetValue", "比较目标值", TestConfigBsj.Data[testItemIndex].Params[paramIndex].TargetValue, false);
            string[] dataSourceEnumCompareTextType = Enum.GetValues(typeof(EnumCompareTextType)).Cast<EnumCompareTextType>().ToList().Select(x => x.ToString()).ToArray();
            option.AddCombobox("ParamCompareTextType", "文本比较", dataSourceEnumCompareTextType, (int)TestConfigBsj.Data[testItemIndex].Params[paramIndex].CompareTextType);
            option.AddDouble("ParamRestoreBase", "还原基数", TestConfigBsj.Data[testItemIndex].Params[paramIndex].RestoreBase);
            option.AddDouble("ParamMinValue", "最小值", TestConfigBsj.Data[testItemIndex].Params[paramIndex].MinValue);
            option.AddDouble("ParamMaxValue", "最大值", TestConfigBsj.Data[testItemIndex].Params[paramIndex].MaxValue);
            option.AddText("ParamUnit", "单位", TestConfigBsj.Data[testItemIndex].Params[paramIndex].Unit, false);
            option.AddText("ParamErrorCode", "错误码", TestConfigBsj.Data[testItemIndex].Params[paramIndex].ErrorCode, false);
            option.AddText("ParamRemark", "备注", TestConfigBsj.Data[testItemIndex].Params[paramIndex].Remark, false);

            // 显示编辑对话框
            BsjUiEditForm frm = new BsjUiEditForm(option);
            frm.Render();
            frm.ShowDialog();
            if (frm.DialogResult != DialogResult.OK)
            {
                return;
            }

            // 编辑对话框关闭后更新测试项数据
            TestConfigBsj.Data[testItemIndex].Name = frm["TestItemName"].ToString();
            TestConfigBsj.Data[testItemIndex].CmdStr = frm["TestItemCmdStr"].ToString();
            TestConfigBsj.Data[testItemIndex].AsciiSend = Convert.ToBoolean(frm["TestItemSwitchAsciiSend"]);
            TestConfigBsj.Data[testItemIndex].EnterLine = Convert.ToBoolean(frm["TestItemSwitchEnterLine"]);
            TestConfigBsj.Data[testItemIndex].ClearData = Convert.ToBoolean(frm["TestItemSwitchClearData"]);
            TestConfigBsj.Data[testItemIndex].EndWithStr = frm["TestItemEndWithStr"].ToString();
            TestConfigBsj.Data[testItemIndex].WaitTime = Convert.ToInt32(frm["TestItemWaitTime"]);
            TestConfigBsj.Data[testItemIndex].WaitCount = Convert.ToInt32(frm["TestItemWaitCount"]);
            TestConfigBsj.Data[testItemIndex].Retry = Convert.ToInt32(frm["TestItemRetry"]);
            TestConfigBsj.Data[testItemIndex].SleepTime = Convert.ToInt32(frm["SleepTime"]);

            // 更新参数数据
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].Name = frm["ParamName"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsEnabled = Convert.ToBoolean(frm["ParamSwitch"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].RegexPattern = frm["ParamRegexPattern"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextType = (EnumCutTextType)Enum.Parse(typeof(EnumCutTextType), frm["ParamCutTextType"].ToString());
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag1 = frm["ParamCutTextFlag1"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CutTextFlag2 = frm["ParamCutTextFlag2"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].IsCompareText = Convert.ToBoolean(frm["ParamSwitchIsCompareText"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].TargetValue = frm["ParamTargetValue"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].CompareTextType = (EnumCompareTextType)Enum.Parse(typeof(EnumCompareTextType), frm["ParamCompareTextType"].ToString());
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].RestoreBase = Convert.ToDouble(frm["ParamRestoreBase"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].MinValue = Convert.ToDouble(frm["ParamMinValue"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].MaxValue = Convert.ToDouble(frm["ParamMaxValue"]);
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].Unit = frm["ParamUnit"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].ErrorCode = frm["ParamErrorCode"].ToString();
            TestConfigBsj.Data[testItemIndex].Params[paramIndex].Remark = frm["ParamRemark"].ToString();

            // 保存到文件
            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH写入SN码")).CmdStr = "AT+WRITE_SN={sn}";
            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH写入SN码")).EndWithStr = "ACK+WRITE_SN:{sn}";
            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH写入SN码")).Params.FirstOrDefault(p => p.Name.Equals("MCU_FLASH写入SN码")).RegexPattern = "ACK.WRITE_SN:{sn}";
            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH写入SN码")).Params.FirstOrDefault(p => p.Name.Equals("MCU_FLASH写入SN码")).TargetValue = "ACK+WRITE_SN:{sn}";

            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH读取SN码")).EndWithStr = "ACK+READ_SN:{sn}";
            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH读取SN码")).Params.FirstOrDefault(p => p.Name.Equals("MCU_FLASH读取SN码")).RegexPattern = "ACK.READ_SN:{sn}";
            // TestConfigBsj.Data.FirstOrDefault(p => p.Name.Equals("MCU_FLASH读取SN码")).Params.FirstOrDefault(p => p.Name.Equals("MCU_FLASH读取SN码")).TargetValue = "ACK+READ_SN:{sn}";
            string json = JsonConvert.SerializeObject(TestConfigBsj, Formatting.Indented);
            File.WriteAllText(Application.StartupPath + "/config/TestConfig.json", json);

            // 更新dgv表格
            LoadTestConfig();
            ShowTestItemsToListView();
            ShowTestItemToEditDgv();

            // 更新MES开关状态
            foreach (TestItem item in TestConfigBsj.Data)
            {
                foreach (TestItemParameter parameter in item.Params)
                {
                    if (parameter.Name.Equals("MES过站"))
                    {
                        IsMesConnect = parameter.IsEnabled;
                        uiSwitch_MES.Active = IsMesConnect;
                    }
                }
            }
        }

        /// <summary>
        /// 取消测试
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiButton_Cancel_Click(object sender, EventArgs e)
        {
            if (Cts != null)
            {
                Cts.Cancel();
            }
        }

        #endregion UI控件事件

        #region 设备串口事件

        /// <summary>
        /// 设备串口数据接收事件处理方法
        /// 当设备串口接收到数据时自动调用此方法
        /// 负责读取串口数据、转换为字符串并累积到缓冲区
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口数据接收事件参数</param>
        private void SerialPortDataReceivedDevice(object sender, SerialDataReceivedEventArgs e)
        {
            SerialPortStream spb = (SerialPortStream)sender;
            try
            {
                // 创建缓冲区读取串口数据
                byte[] receivedData = new byte[spb.BytesToRead];
                int count = spb.Read(receivedData, 0, receivedData.Length);

                // 调试输出接收到的字节数
                Debug.WriteLine($@"设备串口接收字节数: {count}");

                // 将字节数组转换为字符串（使用系统默认编码）
                string recData = Encoding.Default.GetString(receivedData);

                // 过滤空白数据
                if (string.IsNullOrWhiteSpace(recData))
                {
                    return;
                }

                // 将接收到的数据累积到缓冲区
                // 这样可以处理分包接收的情况
                SbDevice.Append(recData);

                // 在日志中显示接收到的数据（不换行，连续显示）
                ShowLog(recData, enterLine: false);
            }
            catch (Exception ex)
            {
                // 记录串口数据接收异常
                ShowLog($"{spb.PortName}: 接收数据异常 - {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// 设备串口错误事件处理方法
        /// 当设备串口发生错误时自动调用此方法
        /// 记录错误信息以便故障排查
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口错误事件参数</param>
        private void SerialPortErrorDevice(object sender, SerialErrorReceivedEventArgs e)
        {
            try
            {
                // 记录串口错误信息
                ShowLog($"{SerialPortHelperDevice.PortName}: 串口错误 - {e.EventType}", LogType.Error);
            }
            catch (Exception ex)
            {
                // 如果记录错误时也发生异常，输出到调试窗口
                Debug.WriteLine($"记录串口错误时发生异常: {ex}");
            }
        }

        /// <summary>
        /// 串口发送数据
        /// </summary>
        /// <param name="comStr"></param>
        /// <param name="asciiSend"></param>
        /// <param name="enterLine"></param>
        private void SerialPortWriteDevice(string comStr, bool asciiSend = true, bool enterLine = true)
        {
            try
            {
                ShowLog($"向{Ai.中括号左}{SerialPortHelperDevice.PortName}{Ai.中括号右}发送指令:{Ai.中括号左}{comStr.Replace("\r\n", "")}{Ai.中括号右}");
                if (asciiSend)
                {
                    if (enterLine)
                    {
                        comStr += "\r\n";
                    }

                    // 将字符串转换为字节数组
                    byte[] dataToSend = Encoding.UTF8.GetBytes(comStr);

                    // 写入数据到串口
                    SerialPortHelperDevice.Write(dataToSend, 0, dataToSend.Length);
                }
                else
                {
                    comStr = comStr.Replace(" ", "");
                    if (enterLine)
                    {
                        comStr += "\r\n";
                    }

                    // 将HEX字符串转换为字节数组
                    byte[] dataToSend = BsjHelper.HexToByte(comStr);

                    // 写入数据到串口
                    SerialPortHelperDevice.Write(dataToSend, 0, dataToSend.Length);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
            }
        }

        #endregion 设备串口事件

        #region 扫码枪串口事件

        /// <summary>
        /// 扫码串口数据接收事件处理方法
        /// 当扫码枪扫描到条码时自动调用此方法
        /// 负责验证SN码、MES查站并启动测试流程
        /// </summary>
        /// <param name="sender">事件发送者（串口对象）</param>
        /// <param name="e">串口数据接收事件参数</param>
        private void SerialPortDataReceivedScan(object sender, SerialDataReceivedEventArgs e)
        {
            SerialPortStream spb = (SerialPortStream)sender;

            // 读取扫码数据
            byte[] receivedData = new byte[spb.BytesToRead];
            int count = spb.Read(receivedData, 0, receivedData.Length);
            string recData = Encoding.Default.GetString(receivedData).Trim();
            Debug.WriteLine($@"扫码接收字节数: {count}");
            ShowLog("扫码结果:" + recData, LogType.Info);

            if (TestDeviceCkInfo == null)
            {
                if (!recData.Length.Equals(Setting.Current.SnLength))
                {
                    ShowLog("输入追溯码:" + Ai.中括号左 + recData + Ai.中括号右 + "长度不正确.请核对检查.", LogType.Error);
                    return;
                }

                ShowLog("追溯码:" + Ai.中括号左 + recData + Ai.中括号右 + "长度正确.", LogType.Error);
                TestDeviceCkInfo = new Se5XDeviceCkInfo
                {
                    TracedCode = recData,
                    TestAppVersion = Application.ProductVersion
                };

                Cts = new CancellationTokenSource();
                Task.Run(DoWork);
            }
            else
            {
                string scanInputLockId = $"扫描到的LockID:{recData}";
                SbDevice.AppendLine(scanInputLockId);
            }
        }

        /// <summary>
        /// 串口错误事件-Scan
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void SerialPortErrorScan(object sender, SerialErrorReceivedEventArgs e)
        {
            try
            {
                ShowLog(SerialPortHelperDevice.PortName + @":串口错误，" + e.EventType, LogType.Error);
            }
            catch (Exception ex)
            {
                ShowLog(ex.ToString());
            }
        }

        #endregion 扫码枪串口事件

        #region 检测逻辑

        /// <summary>
        /// 测试主工作流程
        /// 这是整个自动化测试的核心方法，负责：
        /// 1. 初始化测试环境
        /// 2. 按顺序执行所有测试项
        /// 3. 处理测试结果和异常
        /// 4. 保存测试数据到数据库
        /// </summary>
        private async void DoWork()
        {
            try
            {
                // 初始化测试环境
                TestTime = 0; // 重置测试时间
                SbDevice.Clear(); // 清空串口数据缓冲区
                Invoke(new Action(() => { uiRichTextBox_Log.Clear(); })); // 清空日志显示框
                ReSetTestItemStatus(); // 重置所有测试项状态
                ShowTestStatus("RUN"); // 显示测试状态为运行中

                // 显示测试开始信息
                string tips = string.Empty;
                tips += $"开始测试设备: {TestDeviceCkInfo.Sn}{Environment.NewLine}";
                tips += $"当前测试项目: {TestConfigBsj.Project}{Environment.NewLine}";
                tips += $"测试参数版本: {TestConfigBsj.Version}{Environment.NewLine}";
                tips += $"参数更新时间: {TestConfigBsj.UpdateTime}{Environment.NewLine}";
                ShowLog(tips);
                SbDevice.AppendLine(tips);

                // 开始计算整体测试时间
                Invoke(new Action(() => { timer_ShowTime.Start(); }));

                // 遍历所有测试项组，按配置顺序执行
                foreach (TestItem testItem in TestConfigBsj.Data)
                {
                    // 实现重试机制：如果测试失败，可以重试指定次数
                    for (int retryIndex = 1; retryIndex <= testItem.Retry; retryIndex++)
                    {
                        // 检查是否收到取消测试的请求
                        if (Cts.IsCancellationRequested)
                        {
                            ShowLog("测试已被用户取消", LogType.Warning);
                            return;
                        }

                        // 跳过所有参数都被禁用的测试项组
                        if (!testItem.Params.Any(param => param.IsEnabled))
                        {
                            ShowLog($"测试项组[{testItem.Name}]所有参数均已禁用，跳过", LogType.Info);
                            break;
                        }

                        // 取出当前组中第一个启用项的名称
                        TestItemParameter firstEnabledParam = testItem.Params.FirstOrDefault(x => x.IsEnabled);
                        if (firstEnabledParam != null)
                        {
                            int index = -1;
                            // 通过委托获取索引
                            Invoke(new Action(() => { index = listView_TestItems.Items.IndexOf(listView_TestItems.Items[firstEnabledParam.Name]) + 1; }));
                            tips = Environment.NewLine + Environment.NewLine;
                            tips += $"▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼{Environment.NewLine}";
                            tips += $"{Ai.中括号左}{DateTime.Now:yyyy-MM-dd HH:mm:ss:fff}{Ai.中括号右}";
                            tips += $"开始检测{Ai.中括号左}{index}.{firstEnabledParam.Name}{Ai.中括号右}";
                            ShowLog(tips);
                        }

                        // 根据实际需要延时
                        if (testItem.SleepTime > 0)
                        {
                            ShowLog($@"该项需要延时{Ai.中括号左}{testItem.SleepTime}{Ai.中括号右}毫秒");
                            await Task.Delay(testItem.SleepTime, Cts.Token);
                            ShowLog($@"此处已延时了:{Ai.中括号左}{testItem.SleepTime}{Ai.中括号右}毫秒");
                        }

                        // 执行指令前判断是否需要删除旧的数据
                        if (testItem.ClearData)
                        {
                            SbDevice.Clear();
                        }

                        // 判断发送指令
                        if (!string.IsNullOrWhiteSpace(testItem.CmdStr))
                        {
                            // 预处理测试指令(非固定的动态指令)
                            string tmpCmdStr = testItem.CmdStr;

                            // 预处理动态指令
                            // if (testItem.Name.Equals("蓝牙测试"))
                            // {
                            //     // 先获取val
                            //     BsjDeviceInfo info = await DbHelper.FSql.Select<BsjDeviceInfo>()
                            //         .Where(x =>
                            //             x.TestId == TestDevice.TestId
                            //             && x.Name == "MAC")
                            //         .FirstAsync();
                            //
                            //     tmpCmdStr = tmpCmdStr.Replace("[mac]", $"{info.Value}");
                            // }

                            // 发送测试指令
                            SerialPortWriteDevice(tmpCmdStr, testItem.AsciiSend, testItem.EnterLine);
                        }

                        // 处理独立项
                        if (testItem.Name.Contains("获取PCBA检测数据"))
                        {
                            // 查询PCBA的原始数据
                            Se5XDevicePcba = (await DbHelper.FSql.Select<Se5XDevice>()
                                    .Where(x => x.Sn.Equals(TestDeviceCkInfo.Sn)) // 指定SN号
                                    .Where(x => x.Pass == true) // PASS状态
                                    .ToListAsync())
                                .Last();

                            // 没有找到数据
                            if (Se5XDevicePcba == null)
                            {
                                string infoStr = $@"获取PCBA数据结果失败:{Environment.NewLine}";
                                infoStr += $"未查询到该设备{Ai.中括号左}{TestDeviceCkInfo.Sn}{Ai.中括号右}PCBA检测PASS的数据,请核对检查!";
                                ShowLog(infoStr, LogType.Error);
                                SbDevice.Append(infoStr);
                            }
                            else
                            {
                                string infoStr = $@"获取PCBA数据结果成功:{Environment.NewLine}";
                                infoStr += $@"SN:{Se5XDevicePcba.Sn}{Environment.NewLine}";
                                infoStr += $@"原始IMEI:{Se5XDevicePcba.Sn}{Environment.NewLine}";
                                infoStr += $@"原始IMSI:{Se5XDevicePcba.Sn}{Environment.NewLine}";
                                ShowLog(infoStr, LogType.Info);
                                SbDevice.Append(infoStr);
                            }
                        }
                        else if (testItem.Name.Contains("校验IMEI"))
                        {
                            string infoStr = $"校验IMEI数据明细:{Environment.NewLine}";
                            infoStr += $@"原始IMEI:{Se5XDevicePcba.Imei}{Environment.NewLine}";
                            infoStr += $@"设备IMEI:{TestDeviceCkInfo.Imei}{Environment.NewLine}";
                            SbDevice.AppendLine(infoStr);
                        }
                        else if (testItem.Name.Contains("校验IMSI"))
                        {
                            string infoStr = $"校验IMSI数据明细:{Environment.NewLine}";
                            infoStr += $@"原始IMSI:{Se5XDevicePcba.Imei}{Environment.NewLine}";
                            infoStr += $@"设备IMSI:{TestDeviceCkInfo.Imei}{Environment.NewLine}";
                            SbDevice.AppendLine(infoStr);
                        }
                        else if (testItem.Name.Contains("校验LockID"))
                        {
                            ShowLog("请扫描设备LockID码进行校验", LogType.Error);
                        }
                        else if (testItem.Name.Contains("MES"))
                        {
                            // SN号，工序，用户账号、状态
                            CallBackResultModel<string> result = MESService.PassStation(TestDeviceCkInfo.Sn, Setting.Current.StationName, Setting.Current.UserName, true, "PASS");
                            tips = result.Success + Environment.NewLine;
                            tips += result.Msg + Environment.NewLine;
                            tips += result.Data + Environment.NewLine;
                            ShowLog(tips);
                            SbDevice.Append(tips);
                        }

                        // 监测串口数据
                        string recData = string.Empty;
                        for (int j = 1; j <= testItem.WaitCount; j++)
                        {
                            Thread.Sleep(testItem.WaitTime);

                            // 检测是否手动取消
                            if (Cts.IsCancellationRequested)
                            {
                                return;
                            }

                            // 判断是否有数据
                            if (SbDevice.Length <= 0)
                            {
                                continue;
                            }

                            recData = SbDevice.ToString().Trim();

                            // 判断返回的数据中是否包含关键字
                            if (recData.Contains(testItem.EndWithStr))
                            {
                                break;
                            }
                        }

                        // 判断有无接收到数据
                        if (string.IsNullOrWhiteSpace(recData.Trim()))
                        {
                            if (retryIndex.Equals(testItem.Retry))
                            {
                                ShowLog($@"未检测到测试组{Ai.中括号左}{testItem.Name}{Ai.中括号右}数据,请核对检查!", LogType.Error);
                                SetTestItemStatus(testItem.Params.First(x => x.IsEnabled).Name, EnumTestItemResult.Fail);
                                return;
                            }

                            continue;
                        }

                        // ##################################################################### 遍历测试项
                        foreach (TestItemParameter param in testItem.Params)
                        {
                            // 检测是否手动取消
                            if (Cts.IsCancellationRequested)
                            {
                                return;
                            }

                            // 跳过禁用参数
                            if (!param.IsEnabled)
                            {
                                continue;
                            }

                            // 显示检测前提示信息
                            if (!string.IsNullOrWhiteSpace(param.ShowInfoBefore))
                            {
                                ShowLog(param.ShowInfoBefore, LogType.Error);
                            }

                            // 如果当前测试项不是第一个IsEnabled为true的项,则显示当前测试项
                            if (!param.Name.Equals(testItem.Params.First(x => x.IsEnabled).Name))
                            {
                                int index = -1;
                                // 通过委托获取索引
                                Invoke(new Action(() => { index = listView_TestItems.Items.IndexOf(listView_TestItems.Items[param.Name]) + 1; }));
                                tips = Environment.NewLine + Environment.NewLine;
                                tips += $"▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼{Environment.NewLine}";
                                tips += $"{Ai.中括号左}{DateTime.Now:yyyy-MM-dd HH:mm:ss:fff}{Ai.中括号右}";
                                tips += $"开始检测{Ai.中括号左}{index}.{param.Name}{Ai.中括号右}";
                                ShowLog(tips);
                            }

                            // 创建计时器计算单项测试时间
                            Stopwatch stopwatchSingleItem = new Stopwatch();
                            stopwatchSingleItem.Start();

                            // 预设结果为失败状态
                            bool isPass = false;
                            string tmpGetTargetVal;
                            string tmpTargetValue = param.TargetValue;

                            // 预处理测试数据(非固定的动态数据)

                            // 二次处理获取数据的正则表达式
                            string regexPattern = param.RegexPattern;
                            if (param.Name.Equals("读取SN"))
                            {
                                regexPattern = TestDeviceCkInfo.Sn;
                            }

                            // 先通过正则获取关键参数值
                            Regex regex = new Regex(regexPattern);
                            MatchCollection matches = regex.Matches(recData);
                            if (matches.Count > 0)
                            {
                                tmpGetTargetVal = matches[matches.Count - 1].Value.Trim();
                                // ShowLog($"正则匹配到数据:{tmpGetTargetVal}", LogType.Info);
                                // tips = $"正则匹配到数据:{tmpGetTargetVal}";
                            }
                            else
                            {
                                // 没有匹配到任何数据,且未达到重试次数,则跳出循环再次重试
                                if (retryIndex < testItem.Retry)
                                {
                                    break;
                                }

                                // 已达到重试次数,则输出失败信息,终止所有测试
                                ShowLog($@"未匹配到{Ai.中括号左}{param.Name}{Ai.中括号右}数据,请核对检查!", LogType.Error);
                                SetTestItemStatus(param.Name, EnumTestItemResult.Fail);
                                return;
                            }

                            // 净化数据
                            if (param.CutTextType.Equals(EnumCutTextType.左边))
                            {
                                // ShowLog($"{Environment.NewLine}截取{Ai.中括号左}{param.CutTextFlag1}{Ai.中括号右}左边的数据");
                                tmpGetTargetVal = Ai.GetTextLeft(tmpGetTargetVal, param.CutTextFlag1).Trim();
                            }
                            else if (param.CutTextType.Equals(EnumCutTextType.右边))
                            {
                                // ShowLog($"{Environment.NewLine}截取{Ai.中括号左}{param.CutTextFlag1}{Ai.中括号右}右边的数据");
                                tmpGetTargetVal = Ai.GetTextRight(tmpGetTargetVal, param.CutTextFlag1).Trim();
                            }
                            else if (param.CutTextType.Equals(EnumCutTextType.中间))
                            {
                                // ShowLog($"{Environment.NewLine}截取{Ai.中括号左}{param.CutTextFlag1}{Ai.中括号右}与{Ai.中括号左}{param.CutTextFlag2}{Ai.中括号右}中间的数据");
                                tmpGetTargetVal = Ai.GetTextMiddle(tmpGetTargetVal, param.CutTextFlag1, param.CutTextFlag2).Trim();
                            }

                            // 输出净化后的数据
                            // ShowLog($"{Environment.NewLine}数据净化结果:{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}");

                            // 判断是否为比较文本
                            if (param.IsCompareText)
                            {
                                // 对比目标值
                                if (param.CompareTextType.Equals(EnumCompareTextType.完全一致))
                                {
                                    isPass = tmpGetTargetVal.Equals(tmpTargetValue);
                                }
                                else if (param.CompareTextType.Equals(EnumCompareTextType.目标值包含返回值))
                                {
                                    isPass = tmpTargetValue.Contains(tmpGetTargetVal);
                                }
                                else if (param.CompareTextType.Equals(EnumCompareTextType.返回值包含目标值))
                                {
                                    isPass = tmpGetTargetVal.Contains(tmpTargetValue);
                                }
                                else if (param.CompareTextType.Equals(EnumCompareTextType.非空))
                                {
                                    isPass = !string.IsNullOrWhiteSpace(tmpGetTargetVal);

                                    // 非空情况下需要提取必要信息
                                    if (param.Name.Contains("读取设备LockID"))
                                    {
                                        ShowLog($@"读取到LockID:{tmpGetTargetVal}", LogType.Info);
                                    }

                                    if (param.Name.Contains("读取设备IMEI"))
                                    {
                                        ShowLog($@"读取到IMEI:{tmpGetTargetVal}", LogType.Info);
                                    }

                                    if (param.Name.Contains("读取设备IMSI"))
                                    {
                                        ShowLog($@"读取到IMSI:{tmpGetTargetVal}", LogType.Info);
                                    }

                                    if (param.Name.Contains("读取设备SN"))
                                    {
                                        ShowLog($@"读取到SN:{tmpGetTargetVal}", LogType.Info);
                                    }
                                }
                            }
                            else
                            {
                                double.TryParse(tmpGetTargetVal, out double resVal);
                                // ShowLog($"转换数值:{Ai.中括号左}{resVal}{Ai.中括号右}");

                                if (!param.RestoreBase.Equals(1))
                                {
                                    resVal *= param.RestoreBase;

                                    ShowLog($"原始值:{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右},还原基数:{Ai.中括号左}{param.RestoreBase}{Ai.中括号右}, 还原值:{Ai.中括号左}{resVal}{Ai.中括号右}", LogType.Info);
                                    tmpGetTargetVal = resVal.ToString(CultureInfo.InvariantCulture);
                                }

                                isPass = resVal >= param.MinValue && resVal <= param.MaxValue;
                            }

                            // 判断写入数据库
                            if (IsDbConnect)
                            {
                                // 创建数据库明细对象
                                TestDetail detail = new TestDetail
                                {
                                    ProjectName = TestConfigBsj.Project,
                                    TestId = TestDeviceCkInfo.TestId,
                                    Sn = TestDeviceCkInfo.Sn,
                                    Name = param.Name,
                                    Status = isPass ? "PASS" : "NG",
                                    Val = tmpGetTargetVal,
                                    RefMinVal = param.MinValue.ToString(CultureInfo.InvariantCulture),
                                    RefMaxVal = param.MaxValue.ToString(CultureInfo.InvariantCulture)
                                };

                                // 对比文本的情况下,需要将目标值写入数据库
                                if (param.IsCompareText)
                                {
                                    detail.RefMinVal = tmpTargetValue;
                                }

                                // 将测试明细写入数据库
                                detail.Id = await DbHelper.FSql.Insert<TestDetail>()
                                    .AppendData(detail)
                                    .ExecuteIdentityAsync();
                            }

                            // 输出失败信息
                            if (!isPass)
                            {
                                // 如果没达到重试次数,则跳出循环再次重试
                                if (retryIndex < testItem.Retry)
                                {
                                    break;
                                }

                                // 已达到重试次数,则输出失败信息,结束所有测试
                                string errorMsg = param.IsCompareText
                                    ? $@"{Ai.中括号左}{param.Name}{Ai.中括号右}失败,返回值{Ai.中括号左}{tmpGetTargetVal}{param.Unit}{Ai.中括号右}与目标值{Ai.中括号左}{tmpTargetValue}{param.Unit}{Ai.中括号右}不匹配!"
                                    : $@"{Ai.中括号左}{param.Name}{Ai.中括号右}失败,返回值{Ai.中括号左}{tmpGetTargetVal}{param.Unit}{Ai.中括号右}与目标值{Ai.中括号左}{param.MinValue}{param.Unit}{Ai.中括号右}至{Ai.中括号左}{param.MaxValue}{param.Unit}{Ai.中括号右}不匹配!";

                                // 输出失败信息
                                ShowLog(errorMsg, LogType.Error);
                                SetTestItemStatus(param.Name, EnumTestItemResult.Fail);
                                return;
                            }

                            // 输出成功信息
                            if (param.IsCompareText)
                            {
                                switch (param.CompareTextType)
                                {
                                    case EnumCompareTextType.完全一致:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}与目标值{Ai.中括号左}{tmpTargetValue}{Ai.中括号右}完全一致。", LogType.Info);
                                        break;

                                    case EnumCompareTextType.目标值包含返回值:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,目标值{Ai.中括号左}{tmpTargetValue}{Ai.中括号右}包含返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}。", LogType.Info);
                                        break;

                                    case EnumCompareTextType.返回值包含目标值:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}包含目标值{Ai.中括号左}{tmpTargetValue}{Ai.中括号右}。", LogType.Info);
                                        break;

                                    case EnumCompareTextType.非空:
                                        ShowLog($@"{Ai.中括号左}{param.Name}{param.Unit}{Ai.中括号右}通过,返回值{Ai.中括号左}{tmpGetTargetVal}{Ai.中括号右}", LogType.Info);
                                        break;
                                }
                            }
                            else
                            {
                                ShowLog($@"{Ai.中括号左}{param.Name}{Ai.中括号右}通过,{Ai.中括号左}{tmpGetTargetVal}{param.Unit}{Ai.中括号右}符合目标值范围{Ai.中括号左}{param.MinValue}{param.Unit}{Ai.中括号右}至{Ai.中括号左}{param.MaxValue}{param.Unit}{Ai.中括号右}", LogType.Info);
                            }

                            // 显示检测结果
                            SetTestItemStatus(param.Name, EnumTestItemResult.Pass);

                            // 显示检测后提示信息
                            if (!string.IsNullOrWhiteSpace(param.ShowInfoAfter))
                            {
                                ShowLog(param.ShowInfoAfter, LogType.Error);
                            }

                            // 显示测试项备注信息
                            if (!string.IsNullOrWhiteSpace(param.Remark))
                            {
                                ShowLog(param.Remark, LogType.Error);
                            }

                            // 获取单项检测耗时
                            stopwatchSingleItem.Stop();
                            if (!TestItemTimeDic.ContainsKey(param.Name))
                            {
                                TestItemTimeDic.Add(param.Name, stopwatchSingleItem.ElapsedMilliseconds);
                            }

                            // 判断是否为最后一个需要检测的参数,如果是,则跳出循环进行下一组测试
                            if (param.Name.Equals(testItem.Params.Last(p => p.IsEnabled).Name))
                            {
                                retryIndex = 999;
                            }
                        }
                    }

                    // 显示测试组备注信息
                    if (!string.IsNullOrWhiteSpace(testItem.Remark))
                    {
                        ShowLog(testItem.Remark, LogType.Error);
                    }
                }

                // 设置状态
                TestDeviceCkInfo.Pass = true;
            }
            catch (Exception ex)
            {
                // 输出异常信息
                ShowLog(ex.ToString(), LogType.Error);
            }
            finally
            {
                // 判断是否为手动取消
                if (Cts.IsCancellationRequested)
                {
                    ShowLog("测试被取消", LogType.Error);
                }

                // 停止计时器
                Invoke(new Action(() => { timer_ShowTime.Stop(); }));
                ShowLog($"#################### {Ai.中括号左}测试结束{Ai.中括号右} ####################", LogType.Error);

                // 显示测试结果
                ShowTestStatus(TestDeviceCkInfo.Pass ? "PASS" : "FAIL");

                // 将测试设备记录到数据库
                if (IsDbConnect)
                {
                    await DbHelper.FSql.Insert<Se5XDeviceCkInfo>()
                        .AppendData(TestDeviceCkInfo)
                        .ExecuteIdentityAsync();
                }

                // TestItemTimeDic字典排序
                // string showInfo = Environment.NewLine;
                // IOrderedEnumerable<KeyValuePair<string, long>> sortedByKeyDescending = TestItemTimeDic.OrderBy(pair => pair.Value);
                // foreach (KeyValuePair<string, long> kv in sortedByKeyDescending)
                // {
                //     showInfo += $"单项耗时 => {Ai.中括号左}{kv.Value}{Ai.中括号右} 毫秒 => {Ai.中括号左}{kv.Key}{Ai.中括号右}{Environment.NewLine}";
                // }

                // ShowLog(showInfo);
                ShowLog(@"本轮整体检测耗时: " + Ai.中括号左 + TestTime + Ai.中括号右 + " 秒", LogType.Info);
                ShowLog("请将产品从治具取出,然后扫描下一个SN码开始新一轮检测.", LogType.Error);

                // 写出测试Log到文件
                string logFileName = $"{TestDeviceCkInfo.Sn}_{DateTime.Now:yyyyMMddHHmmss}.txt";
                if (!TestDeviceCkInfo.Pass)
                {
                    logFileName = $"{TestDeviceCkInfo.Sn}_{DateTime.Now:yyyyMMddHHmmss}_FAIL.txt";
                }

                string logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", logFileName);
                Invoke(new Action(() => { File.WriteAllText(logFilePath, uiRichTextBox_Log.Text); }));

                // 测试结束,重置相关参数值
                Cts = null;
                TestDeviceCkInfo = null;
                Se5XDevicePcba = null;
            }
        }

        #endregion 检测逻辑

        #region 设置检测状态

        private void ReSetTestItemStatus()
        {
            Invoke(new Action(() =>
            {
                foreach (ListViewItem item in listView_TestItems.Items)
                {
                    item.ImageIndex = 0;
                }
            }));
        }

        private void SetTestItemStatus(string key, EnumTestItemResult result)
        {
            Invoke(new Action(() =>
            {
                listView_TestItems.Items[key].ImageIndex = (int)result;
                listView_TestItems.EnsureVisible(listView_TestItems.Items[key].Index);
            }));
        }

        private void ShowTestStatus(string resultCode)
        {
            Invoke(new Action(() =>
            {
                uiLabel_Status.Text = resultCode;
                switch (resultCode)
                {
                    case "WAIT":
                        uiLabel_Status.BackColor = Color.LightGray;
                        break;

                    case "RUN":
                        uiLabel_Status.BackColor = Color.LightYellow;
                        break;

                    case "PASS":
                        uiLabel_Status.BackColor = Color.Green;
                        break;

                    default:
                        uiLabel_Status.BackColor = Color.Red;
                        break;
                }
            }));
        }

        #endregion 设置检测状态

        #region 输出日志

        /// <summary>
        /// 显示日志信息到界面
        /// 使用队列机制避免跨线程操作UI控件的问题
        /// 支持不同类型的日志显示（信息、警告、错误等）
        /// </summary>
        /// <param name="logStr">日志内容</param>
        /// <param name="logType">日志类型（决定显示颜色和字体）</param>
        /// <param name="enterLine">是否换行显示</param>
        private void ShowLog(string logStr, LogType logType = LogType.Message, bool enterLine = true)
        {
            // 过滤空白日志内容
            if (string.IsNullOrWhiteSpace(logStr))
            {
                return;
            }

            // 将日志添加到线程安全的队列中，由定时器统一处理
            _logQueue.Enqueue(new CustomLog()
            {
                LogStr = logStr,
                LogType = logType,
                EnterLine = enterLine
            });
        }

        /// <summary>
        /// 初始化日志处理定时器
        /// 定时器用于定期处理日志队列，避免频繁更新UI造成性能问题
        /// </summary>
        private void InitializeLogTimer()
        {
            _logTimer = new Timer { Interval = 250 }; // 每250毫秒处理一次日志队列
            _logTimer.Tick += (sender, e) => ProcessLogQueue();
            _logTimer.Start();
        }

        /// <summary>
        /// 处理日志队列
        /// 从队列中取出日志并显示到界面上
        /// 根据日志类型设置不同的颜色和字体样式
        /// </summary>
        private void ProcessLogQueue()
        {
            // 批量处理队列中的所有日志
            while (_logQueue.TryDequeue(out CustomLog customLog))
            {
                // 根据日志类型设置显示颜色
                Color color = Color.Black;
                switch (customLog.LogType)
                {
                    case LogType.Info:
                        color = Color.Green; // 信息类日志显示为绿色
                        break;

                    case LogType.Error:
                        color = Color.Red; // 错误类日志显示为红色
                        break;

                    case LogType.Warning:
                        color = Color.Orange; // 警告类日志显示为橙色
                        break;
                }

                // 设置文本颜色
                uiRichTextBox_Log.SelectionColor = color;

                // 根据日志类型设置字体样式
                Font font = new Font("微软雅黑", 10);
                if (customLog.LogType == LogType.Error || customLog.LogType == LogType.Info)
                {
                    font = new Font("微软雅黑", 10, FontStyle.Bold); // 错误和信息使用粗体
                }
                else if (customLog.LogType == LogType.Warning)
                {
                    font = new Font("微软雅黑", 10, FontStyle.Italic); // 警告使用斜体
                }
                else if (customLog.LogStr.Contains(Ai.中括号左) && customLog.LogStr.Contains("开始检测") && customLog.LogStr.Contains(Ai.中括号右))
                {
                    font = new Font("微软雅黑", 10, FontStyle.Bold);
                }

                uiRichTextBox_Log.SelectionFont = font; // 设置字体

                // 处理logStr中的空白行
                customLog.LogStr = RemoveConsecutiveBlankLines(customLog.LogStr);
                if (customLog.EnterLine)
                {
                    customLog.LogStr += Environment.NewLine;
                }

                // 添加日志信息,并自动滚动到最后
                uiRichTextBox_Log.AppendText(customLog.LogStr);
                uiRichTextBox_Log.ScrollToCaret();
            }
        }

        private string RemoveConsecutiveBlankLines(string text)
        {
            // 使用正则表达式替换多行连续空白行
            return Regex.Replace(text, @"(\r\n|\r|\n){2,}", "\r\n");
        }

        #endregion 输出日志
    }
}