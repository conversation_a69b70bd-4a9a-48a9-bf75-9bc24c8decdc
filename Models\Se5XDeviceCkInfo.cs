using System;

namespace AppSe5xCheckInfo.Models
{
    /// <summary>
    /// SE5X设备检测信息模型
    ///
    /// 业务说明：
    /// 此模型专门用于记录SE5X设备的检测过程和结果信息。
    /// 与Se5XDevice模型的区别在于，此模型更关注检测过程中的追溯信息，
    /// 包括扫描的追溯码、使用的测试应用版本等检测相关的元数据。
    ///
    /// 使用场景：
    /// 1. PCBA检测流程中的数据记录
    /// 2. 质量追溯和问题定位
    /// 3. 测试过程的完整性验证
    /// 4. 与原始设备数据的关联验证
    ///
    /// 数据关系：
    /// - 通过IMEI与Se5XDevice建立关联
    /// - 一个设备可能有多次检测记录
    /// - 用于验证设备信息的一致性
    /// </summary>
    public class Se5XDeviceCkInfo
    {
        /// <summary>
        /// 检测唯一标识符
        ///
        /// 数据格式：32位大写字母数字组合（无连字符的GUID）
        /// 生成规则：Guid.NewGuid().ToString("N").ToUpper()
        ///
        /// 用途：
        /// - 唯一标识每次检测记录
        /// - 区分同一设备的多次检测
        /// - 关联检测详情和测试参数
        /// - 用于检测数据的追溯和查询
        ///
        /// 与Se5XDevice.TestId的区别：
        /// - Se5XDevice.TestId：原始设备测试的标识
        /// - 此TestId：检测验证过程的标识
        ///
        /// 示例值："B2C3D4E5F6789012345678901234ABC1"
        /// </summary>
        public string TestId { get; set; } = Guid.NewGuid().ToString("N").ToUpper();

        /// <summary>
        /// 检测时间
        ///
        /// 记录内容：设备检测开始的时间戳
        /// 默认值：当前系统时间
        ///
        /// 用途：
        /// - 记录检测执行的具体时间
        /// - 用于检测数据的时间排序
        /// - 检测过程的时间追溯
        /// - 检测效率和周期的统计分析
        ///
        /// 业务意义：
        /// - 可能与原始测试时间不同
        /// - 反映实际的检测验证时间
        /// - 用于检测流程的时间管理
        /// </summary>
        public DateTime TestTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 设备序列号（从数据库查询获得）
        ///
        /// 数据来源：通过IMEI从Se5XDevice表查询获得
        /// 获取时机：在检测过程中通过设备信息关联获取
        ///
        /// 业务逻辑：
        /// - 不是直接扫描输入，而是通过数据库关联获得
        /// - 用于验证TracedCode与实际设备SN的对应关系
        /// - 确保检测的设备与预期的设备一致
        ///
        /// 验证规则：
        /// - 必须与数据库中的原始记录一致
        /// - 用于防止设备混淆和错误检测
        ///
        /// 注意事项：
        /// - 此字段在检测过程中被自动填充
        /// - 不应该手动修改
        /// </summary>
        public string Sn { get; set; }

        /// <summary>
        /// 追溯码（扫描输入的标识码）
        ///
        /// 数据来源：通过扫码枪扫描获得的输入
        /// 数据格式：根据SettingConfig.SnLength定义的长度
        ///
        /// 业务含义：
        /// - 操作员扫描的设备标识码
        /// - 可能是SN码、条形码或其他追溯标识
        /// - 用于启动检测流程的触发标识
        ///
        /// 验证逻辑：
        /// - 长度必须符合配置要求
        /// - 用于查找对应的设备记录
        /// - 与数据库中的SN进行关联验证
        ///
        /// 使用流程：
        /// 1. 扫描获得TracedCode
        /// 2. 通过TracedCode查找设备信息
        /// 3. 验证设备存在性和有效性
        /// 4. 开始检测流程
        /// </summary>
        public string TracedCode { get; set; }

        /// <summary>
        /// 锁具标识符（从设备读取）
        ///
        /// 数据来源：通过AT指令从设备内部读取
        /// 获取命令：AT+INFO指令响应中的LockID字段
        ///
        /// 业务用途：
        /// - 验证设备内部标识的正确性
        /// - 与外部标签或扫描的LockId进行比对
        /// - 确保设备内外标识的一致性
        ///
        /// 验证流程：
        /// 1. 从设备读取内部LockId
        /// 2. 提示操作员扫描外部LockId
        /// 3. 比较两者是否一致
        /// 4. 一致则通过，不一致则失败
        ///
        /// 默认值：空字符串（待读取）
        /// </summary>
        public string LockId { get; set; } = string.Empty;

        /// <summary>
        /// 国际移动设备识别码（从设备读取）
        ///
        /// 数据来源：通过AT指令从设备通信模块读取
        /// 获取命令：AT+INFO指令响应中的HardwareID字段
        ///
        /// 关键作用：
        /// - 作为设备的唯一标识符
        /// - 用于在数据库中查找原始设备记录
        /// - 建立检测记录与原始记录的关联
        ///
        /// 验证逻辑：
        /// - 必须在数据库中存在对应的Se5XDevice记录
        /// - 该记录的Pass字段必须为true（原始测试通过）
        /// - 用于确保只对合格设备进行检测
        ///
        /// 数据格式：15位数字字符串
        /// 示例："123456789012345"
        /// </summary>
        public string Imei { get; set; }

        /// <summary>
        /// 国际移动用户识别码（从设备读取）
        ///
        /// 数据来源：通过AT指令从设备读取
        /// 获取命令：AT+INFO指令响应中的DeviceID字段
        ///
        /// 用途：
        /// - 记录设备的用户标识信息
        /// - 用于完整性验证
        /// - 与原始记录进行比对
        ///
        /// 注意事项：
        /// - 可能为空（设备未配置或无SIM卡）
        /// - 主要用于信息记录，不作为关键验证字段
        ///
        /// 数据格式：最多15位数字字符串
        /// </summary>
        public string Imsi { get; set; }

        /// <summary>
        /// 测试应用程序版本
        ///
        /// 数据来源：Assembly.GetExecutingAssembly().GetName().Version
        /// 记录内容：执行检测的应用程序版本号
        ///
        /// 业务价值：
        /// - 用于追溯检测时使用的软件版本
        /// - 便于问题定位和版本管理
        /// - 确保检测结果的可追溯性
        /// - 支持不同版本间的兼容性分析
        ///
        /// 格式示例：
        /// - "1.0.0.0"
        /// - "2.1.3.456"
        ///
        /// 设置时机：
        /// - 在创建检测记录时自动设置
        /// - 反映实际执行检测的软件版本
        ///
        /// 默认值：空字符串（待设置）
        /// </summary>
        public string TestAppVersion { get; set; } = string.Empty;

        /// <summary>
        /// 检测通过标志
        ///
        /// 判断依据：所有检测项目都通过验证
        /// 默认值：false（未通过）
        ///
        /// 检测项目包括：
        /// 1. 设备通信验证（AT+PWD指令）
        /// 2. 设备信息获取（AT+INFO指令）
        /// 3. N码绑定验证（TracedCode与数据库记录匹配）
        /// 4. LockId一致性验证（内部与外部标识匹配）
        /// 5. MES系统过站（如果启用）
        ///
        /// 业务影响：
        /// - 决定设备是否可以进入下一工序
        /// - 影响质量统计和报告
        /// - 触发MES系统状态更新
        ///
        /// 设置逻辑：
        /// - 只有所有检测项都通过才设置为true
        /// - 任何一项失败都保持false
        /// - 在检测流程最后统一设置
        /// </summary>
        public bool Pass { get; set; }
    }
}