using System;

namespace AppSe5xCheckInfo.Models
{
    /// <summary>
    /// SE5X设备基础信息模型
    ///
    /// 业务说明：
    /// 此模型用于存储SE5X设备的基本信息和测试结果。
    /// SE5X是一种智能锁设备，需要在生产过程中进行PCBA测试，
    /// 确保设备的通信功能、身份标识等关键参数正常。
    ///
    /// 数据来源：
    /// 1. 通过AT指令从设备获取硬件信息
    /// 2. 从生产系统或MES系统获取序列号
    /// 3. 测试过程中生成的验证结果
    ///
    /// 存储用途：
    /// - 作为原始设备数据的记录
    /// - 用于后续的设备信息查询和验证
    /// - 与Se5XDeviceCkInfo配合使用进行数据关联
    /// </summary>
    public class Se5XDevice
    {
        /// <summary>
        /// 测试唯一标识符
        ///
        /// 数据格式：32位大写字母数字组合（无连字符的GUID）
        /// 生成规则：Guid.NewGuid().ToString("N").ToUpper()
        ///
        /// 用途：
        /// - 唯一标识每次测试记录
        /// - 用于数据库主键或索引
        /// - 关联测试详情和设备信息
        /// - 便于测试数据的追溯和查询
        ///
        /// 示例值："A1B2C3D4E5F6789012345678901234AB"
        /// </summary>
        public string TestId { get; set; } = Guid.NewGuid().ToString("N").ToUpper();

        /// <summary>
        /// 测试时间
        ///
        /// 记录内容：设备测试开始的时间戳
        /// 默认值：当前系统时间
        ///
        /// 用途：
        /// - 记录测试执行的具体时间
        /// - 用于测试数据的时间排序
        /// - 生产过程的时间追溯
        /// - 测试效率和周期的统计分析
        ///
        /// 注意事项：
        /// - 时间精度到毫秒级
        /// - 建议使用UTC时间避免时区问题
        /// </summary>
        public DateTime TestTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 设备序列号（Serial Number）
        ///
        /// 数据来源：生产系统分配或从设备内部读取
        /// 格式要求：根据产品规格定义，通常为字母数字组合
        ///
        /// 业务意义：
        /// - 设备的唯一身份标识
        /// - 用于产品追溯和售后服务
        /// - MES系统中的关键标识符
        /// - 与生产订单和批次信息关联
        ///
        /// 使用场景：
        /// - 设备出厂标识
        /// - 质量追溯
        /// - 保修服务
        /// - 库存管理
        /// </summary>
        public string Sn { get; set; }

        /// <summary>
        /// 锁具标识符
        ///
        /// 数据来源：设备内部存储或外部标签
        /// 业务含义：智能锁的物理标识，用于设备配对和管理
        ///
        /// 用途：
        /// - 智能锁的唯一物理标识
        /// - 用于设备配对和绑定
        /// - 现场安装和维护的参考
        /// - 与设备管理系统的关联标识
        ///
        /// 验证要求：
        /// - 必须与设备内部存储的LockId一致
        /// - 用于防止设备混装和错配
        ///
        /// 默认值：空字符串（待获取）
        /// </summary>
        public string LockId { get; set; } = string.Empty;

        /// <summary>
        /// 国际移动设备识别码（International Mobile Equipment Identity）
        ///
        /// 数据格式：15位数字字符串
        /// 获取方式：通过AT指令从设备通信模块读取
        ///
        /// 业务意义：
        /// - 移动通信设备的全球唯一标识
        /// - 用于网络接入认证和设备管理
        /// - 防盗和安全管理的重要标识
        ///
        /// 技术规范：
        /// - 符合3GPP TS 23.003标准
        /// - 包含TAC（类型分配码）和SNR（序列号）
        /// - 最后一位为校验位（Luhn算法）
        ///
        /// 示例格式："123456789012345"
        /// </summary>
        public string Imei { get; set; }

        /// <summary>
        /// 国际移动用户识别码（International Mobile Subscriber Identity）
        ///
        /// 数据格式：最多15位数字字符串
        /// 获取方式：通过AT指令从SIM卡或设备读取
        ///
        /// 业务意义：
        /// - 移动网络用户的唯一标识
        /// - 用于网络认证和计费
        /// - 与运营商服务关联
        ///
        /// 技术规范：
        /// - 符合ITU-T E.212标准
        /// - 包含MCC（移动国家码）、MNC（移动网络码）、MSIN（移动用户识别码）
        ///
        /// 注意事项：
        /// - 可能为空（无SIM卡或未配置）
        /// - 涉及用户隐私，需要安全处理
        ///
        /// 示例格式："123456789012345"
        /// </summary>
        public string Imsi { get; set; }

        /// <summary>
        /// 测试通过标志
        ///
        /// 判断依据：所有测试项目都通过验证
        /// 默认值：false（未通过）
        ///
        /// 业务逻辑：
        /// - true：设备所有功能正常，可以出厂
        /// - false：设备存在问题，需要返工或报废
        ///
        /// 影响范围：
        /// - 生产流程控制
        /// - 质量统计分析
        /// - MES系统状态更新
        /// - 设备放行决策
        ///
        /// 设置时机：
        /// - 在所有测试项目完成后统一设置
        /// - 任何一项测试失败都会导致整体失败
        /// </summary>
        public bool Pass { get; set; }
    }
}